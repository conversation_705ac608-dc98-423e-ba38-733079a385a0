<!DOCTYPE html>
<html lang="id" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portofolio | Rofikul Huda</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Menggunakan font-family yang lebih spesifik untuk menargetkan San Francisco di semua perangkat */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            background-color: #0A0A10;
            color: #E5E7EB;
            overflow-x: hidden;
        }

        /* Latar Matrix */
        #matrix-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            opacity: 0.5;
        }

        /* Scanline Overlay */
        .scanline-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            pointer-events: none;
            background: linear-gradient(rgba(0,0,0,0) 50%, rgba(0,0,0,0.2) 50%), linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 100% 4px, 3px 3px;
            animation: scanline 15s linear infinite;
        }
        @keyframes scanline {
            from { background-position: 0 0; }
            to { background-position: 0 100%; }
        }

        /* Terminal Preloader */
        #preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            background-color: #000000;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease, visibility 0.5s ease;
            font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .terminal-loader {
            background-color: #0A0A10;
            border: 2px solid #10B981;
            border-radius: 8px;
            padding: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow:
                0 0 20px rgba(16, 185, 129, 0.3),
                inset 0 0 20px rgba(16, 185, 129, 0.1);
        }

        .terminal-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #10B981;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot-red { background-color: #ff5f56; }
        .dot-yellow { background-color: #ffbd2e; }
        .dot-green { background-color: #27ca3f; }

        .terminal-title {
            color: #10B981;
            font-size: 14px;
            margin-left: 10px;
        }

        .terminal-content {
            color: #10B981;
            font-size: 14px;
            line-height: 1.6;
        }

        .loading-line {
            margin: 8px 0;
            opacity: 0;
            animation: fadeInLine 0.5s ease-in-out forwards;
        }

        .loading-line.prompt::before {
            content: 'root@rfiklz:~# ';
            color: #10B981;
            font-weight: bold;
        }

        .loading-line.output {
            color: #34D399;
            margin-left: 20px;
        }

        .loading-line.error {
            color: #EF4444;
            margin-left: 20px;
        }

        .loading-line.success {
            color: #10B981;
            margin-left: 20px;
        }

        .cursor-blink {
            display: inline-block;
            background-color: #10B981;
            width: 8px;
            height: 16px;
            animation: blink 1s step-end infinite;
        }

        .progress-bar {
            margin: 15px 0;
            background-color: #1a1a1a;
            border: 1px solid #10B981;
            border-radius: 4px;
            overflow: hidden;
            height: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10B981, #34D399);
            width: 0%;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes fadeInLine {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Efek kaca cair (liquid glass) dengan efek 3D Tilt */
        .liquid-glass-effect {
            background: rgba(16, 185, 129, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(52, 211, 153, 0.2);
            border-radius: 1.5rem; /* Rounded dikurangi lagi */
            transition: all 0.3s ease-out;
            transform-style: preserve-3d;
        }
        .liquid-glass-effect:hover {
            border-color: rgba(52, 211, 153, 0.5);
            box-shadow: 0 10px 40px rgba(16, 185, 129, 0.1);
        }
        
        /* Kelas untuk navigasi aktif dan hover glitch */
        .nav-active {
            background-color: rgba(16, 185, 129, 0.3) !important;
            color: white !important;
        }
        .glitch-hover:hover {
            animation: glitch 0.3s linear;
        }
        @keyframes glitch{
          0% { text-shadow: .05em 0 0 rgba(255,0,0,.75), -.025em -.05em 0 rgba(0,255,0,.75), .025em .05em 0 rgba(0,0,255,.75); }
          14% { text-shadow: .05em 0 0 rgba(255,0,0,.75), -.025em -.05em 0 rgba(0,255,0,.75), .025em .05em 0 rgba(0,0,255,.75); }
          15% { text-shadow: -.05em -.025em 0 rgba(255,0,0,.75), .025em .025em 0 rgba(0,255,0,.75), -.05em -.05em 0 rgba(0,0,255,.75); }
          49% { text-shadow: -.05em -.025em 0 rgba(255,0,0,.75), .025em .025em 0 rgba(0,255,0,.75), -.05em -.05em 0 rgba(0,0,255,.75); }
          50% { text-shadow: .025em .05em 0 rgba(255,0,0,.75), .05em 0 0 rgba(0,255,0,.75), 0 -.05em 0 rgba(0,0,255,.75); }
          99% { text-shadow: .025em .05em 0 rgba(255,0,0,.75), .05em 0 0 rgba(0,255,0,.75), 0 -.05em 0 rgba(0,0,255,.75); }
          100% { text-shadow: -.025em 0 0 rgba(255,0,0,.75), -.025em -.025em 0 rgba(0,255,0,.75), -.025em -.05em 0 rgba(0,0,255,.75); }
        }
        
        #mobile-menu { max-height: 0; overflow: hidden; transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out; }
        #mobile-menu.open { padding: 0.5rem; }

        .page-content { animation: fadeIn 0.6s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px) scale(0.98); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        
        .project-card-image { border-radius: 1.4rem; } /* Rounded gambar juga dikurangi */
        
        /* Terminal Styling */
        .terminal-window { font-family: 'Courier New', Courier, monospace; }
        .terminal-header { background-color: #1a1a1a; }
        .terminal-body { background-color: rgba(0,0,0,0.5); }
        .prompt::before { content: 'user@rfypych:~$ '; color: #10B981; }
        .blinking-cursor {
            display: inline-block;
            background-color: #10B981;
            color: #0A0A10;
            animation: terminalBlink 1.2s step-end infinite;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(16, 185, 129, 0.8);
            font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        @keyframes terminalBlink {
            0%, 60% {
                opacity: 1;
                background-color: #10B981;
                box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
            }
            61%, 100% {
                opacity: 0.3;
                background-color: transparent;
                box-shadow: none;
            }
        }

        /* Enhanced typewriter container */
        #typewriter-container {
            font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            letter-spacing: 0.05em;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
            min-height: 2em;
            display: flex;
            align-items: center;
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        #typewriter-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
            animation: scanline 3s linear infinite;
        }

        @keyframes scanline {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Terminal prompt styling */
        .terminal-prompt {
            color: #10B981;
            font-weight: bold;
        }

        .terminal-output {
            color: #34D399;
            margin-left: 8px;
        }

        /* Animasi mengambang untuk hero card */
        #hero .tilt-card {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        /* Animasi pulse untuk emoji */
        .pulse-emoji {
            display: inline-block;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        /* Gaya teks hero yang keren tanpa animasi */
        .hero-title {
            background: linear-gradient(135deg, #10B981, #34D399, #6EE7B7);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
            position: relative;
        }

        .hero-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent, rgba(16, 185, 129, 0.1), transparent);
            border-radius: 10px;
            z-index: -1;
            filter: blur(20px);
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Efek hologram untuk teks */
        .hologram-text {
            position: relative;
            color: #10B981;
            text-shadow:
                0 0 5px #10B981,
                0 0 10px #10B981,
                0 0 15px #10B981,
                0 0 20px #10B981;
        }

        .hologram-text::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            color: rgba(16, 185, 129, 0.3);
            z-index: -1;
            animation: hologramFlicker 3s infinite;
        }

        @keyframes hologramFlicker {
            0%, 100% { opacity: 0.3; transform: translateX(0); }
            25% { opacity: 0.7; transform: translateX(1px); }
            50% { opacity: 0.5; transform: translateX(-1px); }
            75% { opacity: 0.8; transform: translateX(0.5px); }
        }

        /* Terminal font styling untuk nama */
        .terminal-name {
            font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            letter-spacing: 0.1em;
            text-shadow:
                0 0 10px rgba(16, 185, 129, 0.5),
                0 0 20px rgba(16, 185, 129, 0.3),
                0 0 30px rgba(16, 185, 129, 0.2);
        }

        /* GitHub Contribution Graph */
        .contribution-grid {
            display: grid;
            grid-template-columns: repeat(53, 1fr);
            gap: 2px;
            max-width: 100%;
            overflow-x: auto;
            padding: 1rem;
        }

        .contribution-day {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }

        .contribution-day:hover {
            transform: scale(1.3);
            border: 1px solid #10B981;
            z-index: 10;
        }

        .contribution-level-0 { background-color: #161b22; }
        .contribution-level-1 { background-color: #0e4429; }
        .contribution-level-2 { background-color: #006d32; }
        .contribution-level-3 { background-color: #26a641; }
        .contribution-level-4 { background-color: #39d353; }





        /* Enhanced Navigation Hover Effects */
        .nav-link {
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #10B981, #34D399);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before {
            width: 80%;
        }

        .nav-link.nav-active::before {
            width: 80%;
        }

        /* Enhanced Mobile Menu */
        #mobile-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #mobile-menu.open {
            max-height: 300px;
        }

        .mobile-nav-link {
            transform: translateX(-20px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        #mobile-menu.open .mobile-nav-link {
            transform: translateX(0);
            opacity: 1;
        }

        #mobile-menu.open .mobile-nav-link:nth-child(1) { transition-delay: 0.1s; }
        #mobile-menu.open .mobile-nav-link:nth-child(2) { transition-delay: 0.2s; }
        #mobile-menu.open .mobile-nav-link:nth-child(3) { transition-delay: 0.3s; }
        #mobile-menu.open .mobile-nav-link:nth-child(4) { transition-delay: 0.4s; }

        /* Responsive design improvements */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem !important;
            }

            .terminal-name {
                letter-spacing: 0.05em;
            }

            .contribution-grid {
                grid-template-columns: repeat(26, 1fr);
            }

            .contribution-day {
                width: 10px;
                height: 10px;
            }
        }

    </style>
</head>
<body class="text-gray-200 leading-relaxed">

    <!-- Terminal Preloader -->
    <div id="preloader">
        <div class="terminal-loader">
            <div class="terminal-header">
                <div class="terminal-dot dot-red"></div>
                <div class="terminal-dot dot-yellow"></div>
                <div class="terminal-dot dot-green"></div>
                <span class="terminal-title">rfiklz@terminal</span>
            </div>
            <div class="terminal-content" id="terminal-loading">
                <!-- Loading content will be populated by JavaScript -->
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
        </div>
    </div>
    
    <!-- Latar Matrix -->
    <canvas id="matrix-canvas"></canvas>

    <!-- Overlay -->
    <div class="scanline-overlay"></div>
    
    <!-- Konten Utama -->
    <div class="relative z-10">
        <!-- Header -->
        <header class="p-4 fixed w-full top-0 z-50">
            <div class="container mx-auto px-4">
                 <nav id="main-nav" class="liquid-glass-effect p-2 px-6 flex justify-between items-center" style="border-radius: 9999px;">
                    <a href="#" data-page="hero" class="nav-link glitch-hover text-xl font-bold text-white transition-colors">Rofikul Huda</a>
                    <div class="hidden md:flex items-center space-x-2">
                        <a href="#" data-page="about" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">About</a>
                        <a href="#" data-page="skills" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Skills</a>
                        <a href="#" data-page="projects" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Projects</a>
                        <a href="#" data-page="contact" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Contact</a>
                    </div>
                    <div class="md:hidden"><button id="mobile-menu-button" class="text-white focus:outline-none"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg></button></div>
                 </nav>
                 <div id="mobile-menu" class="md:hidden mt-2 liquid-glass-effect rounded-2xl">
                    <a href="#" data-page="about" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">About</a>
                    <a href="#" data-page="skills" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Skills</a>
                    <a href="#" data-page="projects" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Projects</a>
                    <a href="#" data-page="contact" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Contact</a>
                </div>
            </div>
        </header>

        <main id="main-content" class="container mx-auto px-4 pt-40" style="perspective: 1000px;">
            <!-- Section Hero -->
            <section id="hero" class="page-content min-h-[calc(100vh-10rem)] flex items-center text-center">
                <div class="w-full">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">
                        <!-- Left Content -->
                        <div class="liquid-glass-effect p-8 md:p-12 shadow-2xl tilt-card">
                            <p class="text-2xl text-gray-300 mb-4">Hey there! 👋🏻</p>
                            <h1 class="hero-title text-4xl md:text-6xl font-bold mb-4">
                                <span class="terminal-name">I'M</span>
                                <br>
                                <span class="terminal-name">ROFIKUL HUDA</span>
                            </h1>
                            <div id="typewriter-container" class="text-xl md:text-2xl text-emerald-300 mb-6 h-8"></div>
                            <p class="text-lg text-gray-300 mb-8 leading-relaxed">
                                I'm passionate about coding and constantly exploring new technologies.
                                Every day brings fresh challenges and learning opportunities that keep me motivated! 🚀
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="#" id="intro-button" class="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-emerald-500/25 inline-block relative overflow-hidden">
                                    <span class="relative z-10">🚀 Explore Now</span>
                                </a>
                                <a href="#" data-page="contact" class="border-2 border-emerald-600 text-emerald-400 hover:bg-emerald-600 hover:text-white font-bold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-emerald-500/25 inline-block">
                                    <span>💬 Get In Touch</span>
                                </a>
                            </div>
                        </div>

                        <!-- Right Content - Avatar & Social -->
                        <div class="liquid-glass-effect p-8 shadow-2xl tilt-card">
                            <div class="flex flex-col items-center">
                                <div class="relative mb-6">
                                    <img src="profile.png" alt="Rofikul Huda - Developer & Tech Enthusiast" class="rounded-full w-48 h-48 md:w-64 md:h-64 object-cover border-4 border-emerald-400/30 shadow-lg hover:shadow-emerald-500/30 transition-all duration-300 hover:scale-105">
                                    <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-emerald-500 rounded-full border-4 border-gray-900 animate-pulse"></div>
                                    <div class="absolute inset-0 rounded-full bg-gradient-to-tr from-emerald-500/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-4">CONNECT WITH ME</h3>
                                <p class="text-gray-300 mb-6">Let's connect and <span class="text-emerald-300">collaborate!</span> Find me here</p>
                                <div class="flex gap-4">
                                    <a href="https://github.com/rfypych" target="_blank" class="w-12 h-12 bg-gray-800 hover:bg-emerald-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/></svg>
                                    </a>
                                    <a href="https://t.me/rfyycrnge" target="_blank" class="w-12 h-12 bg-gray-800 hover:bg-emerald-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/></svg>
                                    </a>
                                    <a href="https://www.instagram.com/rfikl_" target="_blank" class="w-12 h-12 bg-gray-800 hover:bg-emerald-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Tentang Saya -->
            <section id="about" class="page-content hidden py-24">
                <div class="max-w-7xl mx-auto space-y-16">
                    <!-- Main About Card -->
                    <div class="liquid-glass-effect p-8 md:p-12 shadow-2xl tilt-card">
                        <h2 class="text-4xl font-bold text-white text-center mb-12">LET ME <span class="text-emerald-300">INTRODUCE</span> MYSELF</h2>
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                            <div class="lg:col-span-1 flex justify-center">
                                <div class="relative">
                                    <img src="profile.png" alt="Rofikul Huda - Developer & Tech Enthusiast" class="rounded-full w-48 h-48 md:w-64 md:h-64 object-cover border-4 border-emerald-400/30 shadow-lg hover:shadow-emerald-500/30 transition-all duration-300 hover:scale-105">
                                    <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-emerald-500 rounded-full border-4 border-gray-900 animate-pulse"></div>
                                    <div class="absolute inset-0 rounded-full bg-gradient-to-tr from-emerald-500/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                                </div>
                            </div>                            <div class="lg:col-span-2 text-gray-300 text-lg space-y-6">
                                <h3 class="text-2xl font-bold text-white mb-4">Know Who I Am</h3>
                                <p class="text-xl leading-relaxed">Greetings! I'm Rofikul Huda, a technology enthusiast from East Java, Indonesia. Currently, I'm focusing on advancing my expertise in <b class="text-emerald-300">Ethical Hacking</b> and <b class="text-emerald-300">Web Development</b>. 🚀</p>
                                <p class="leading-relaxed">As a Computer and Network Engineering student at SMK PGRI 1 Ngawi, I'm deeply immersed in the world of technology. Every day presents new opportunities for learning and growth, and I'm fully committed to this exciting journey.</p>
                                <div class="mt-8">
                                    <h4 class="text-xl font-semibold text-emerald-300 mb-4">Beyond Technology</h4>
                                    <div class="space-y-2">
                                        <p class="flex items-center gap-3"><span class="text-2xl">🎮</span> Exploring virtual worlds through gaming</p>
                                        <p class="flex items-center gap-3"><span class="text-2xl">🎧</span> Finding inspiration in music</p>
                                        <p class="flex items-center gap-3"><span class="text-2xl">📱</span> Engaging with the latest trends on Threads</p>
                                    </div>
                                </div>
                                <div class="mt-8 p-6 liquid-glass-effect rounded-xl">
                                    <blockquote class="italic text-emerald-300">"I have no special talent. I am only passionately curious."</blockquote>
                                    <p class="text-right mt-2 text-gray-400">- Albert Einstein</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="liquid-glass-effect p-6 text-center tilt-card hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/20">
                            <div class="text-3xl font-bold text-emerald-300 mb-2 animate-pulse">2+</div>
                            <div class="text-gray-300 font-semibold">Years of Learning</div>
                            <div class="text-sm text-gray-400 mt-2">Continuous journey in technology</div>
                        </div>
                        <div class="liquid-glass-effect p-6 text-center tilt-card hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/20">
                            <div class="text-3xl font-bold text-emerald-300 mb-2 animate-pulse">15+</div>
                            <div class="text-gray-300 font-semibold">Technologies</div>
                            <div class="text-sm text-gray-400 mt-2">Programming languages & tools</div>
                        </div>
                        <div class="liquid-glass-effect p-6 text-center tilt-card hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/20">
                            <div class="text-3xl font-bold text-emerald-300 mb-2 animate-pulse">∞</div>
                            <div class="text-gray-300 font-semibold">Curiosity Level</div>
                            <div class="text-sm text-gray-400 mt-2">Always eager to learn more</div>
                        </div>
                        <div class="liquid-glass-effect p-6 text-center tilt-card hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/20">
                            <div class="text-3xl font-bold text-emerald-300 mb-2" id="github-commits">Loading...</div>
                            <div class="text-gray-300 font-semibold">GitHub Commits</div>
                            <div class="text-sm text-gray-400 mt-2">This year's contributions</div>
                        </div>
                    </div>

                    <!-- Journey Timeline -->
                    <div id="journey" class="liquid-glass-effect p-8 md:p-12 shadow-2xl tilt-card">
                        <h3 class="text-3xl font-bold text-white text-center mb-12">My <span class="text-emerald-300">Journey</span></h3>
                        <div class="space-y-8">
                            <div class="flex items-start gap-4">
                                <div class="w-4 h-4 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                                <div>
                                    <h4 class="text-xl font-bold text-emerald-300 mb-2">Started My Coding Journey</h4>
                                    <p class="text-gray-300">Began with HTML, CSS, and JavaScript fundamentals. Built my first website and discovered my passion for development!</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4">
                                <div class="w-4 h-4 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                                <div>
                                    <h4 class="text-xl font-bold text-emerald-300 mb-2">Explored Backend & Security</h4>
                                    <p class="text-gray-300">Expanded into Node.js, Python, and cybersecurity. Started learning ethical hacking and network security principles.</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4">
                                <div class="w-4 h-4 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                                <div>
                                    <h4 class="text-xl font-bold text-emerald-300 mb-2">AI & Modern Development</h4>
                                    <p class="text-gray-300">Embracing AI tools and modern frameworks. Currently building projects with React, Next.js, and integrating AI solutions.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- GitHub Contribution Graph -->
                    <div id="github-activity" class="liquid-glass-effect p-8 shadow-2xl tilt-card">
                        <h3 class="text-3xl font-bold text-white text-center mb-8">GitHub <span class="text-emerald-300">Activity</span></h3>

                        <!-- Custom Contribution Graph -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-lg font-semibold text-gray-300">Contributions in the last year</h4>
                                <div class="flex items-center gap-2 text-sm text-gray-400">
                                    <span>Less</span>
                                    <div class="flex gap-1">
                                        <div class="w-3 h-3 bg-gray-800 rounded-sm"></div>
                                        <div class="w-3 h-3 bg-emerald-900 rounded-sm"></div>
                                        <div class="w-3 h-3 bg-emerald-700 rounded-sm"></div>
                                        <div class="w-3 h-3 bg-emerald-500 rounded-sm"></div>
                                        <div class="w-3 h-3 bg-emerald-300 rounded-sm"></div>
                                    </div>
                                    <span>More</span>
                                </div>
                            </div>
                            <div id="contribution-graph" class="bg-gray-900/50 rounded-lg border border-emerald-500/20 min-h-[120px] flex items-center justify-center">
                                <div class="text-emerald-300 animate-pulse">Loading GitHub contributions...</div>
                            </div>
                            <div class="text-center mt-4">
                                <span class="text-emerald-300 font-bold" id="total-contributions">Loading...</span>
                                <span class="text-gray-400"> contributions in the last year</span>
                            </div>
                        </div>

                        <!-- Real GitHub Stats Cards -->
                        <div class="text-center mb-6">
                            <img src="https://github-readme-stats.vercel.app/api?username=rfypych&show_icons=true&theme=radical&bg_color=0A0A10&title_color=10B981&icon_color=34D399&text_color=E5E7EB&border_color=10B981&include_all_commits=true&count_private=false" alt="GitHub Stats" class="mx-auto rounded-lg shadow-lg max-w-full">
                        </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="text-center">
                                <img src="https://github-readme-streak-stats.herokuapp.com/?user=rfypych&theme=radical&background=0A0A10&border=10B981&stroke=10B981&ring=34D399&fire=10B981&currStreakLabel=10B981" alt="GitHub Streak" class="mx-auto rounded-lg shadow-lg max-w-full">
                            </div>
                            <div class="text-center">
                                <img src="https://github-readme-stats.vercel.app/api/top-langs/?username=rfypych&layout=compact&theme=radical&bg_color=0A0A10&title_color=10B981&text_color=E5E7EB&border_color=10B981&langs_count=8" alt="Top Languages" class="mx-auto rounded-lg shadow-lg max-w-full">
                            </div>
                        </div>

                        <!-- Additional GitHub Metrics -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div class="text-center">
                                <img src="https://github-profile-summary-cards.vercel.app/api/cards/profile-details?username=rfypych&theme=radical" alt="GitHub Profile Summary" class="mx-auto rounded-lg shadow-lg max-w-full">
                            </div>
                            <div class="text-center">
                                <img src="https://github-profile-summary-cards.vercel.app/api/cards/productive-time?username=rfypych&theme=radical&utcOffset=7" alt="GitHub Productive Time" class="mx-auto rounded-lg shadow-lg max-w-full">
                            </div>
                        </div>
                        <div class="text-center mt-6">
                            <a href="https://github.com/rfypych" target="_blank" class="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/></svg>
                                View Full Profile
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Keahlian -->
            <section id="skills" class="page-content hidden py-24">
                <h2 class="text-4xl font-bold text-center text-white mb-16">Technologies I'm <span class="text-emerald-300">Passionate</span> About</h2>
                <div class="max-w-6xl mx-auto grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg" alt="Linux Icon" class="w-12 h-12"><span class="font-medium">Linux</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg" alt="HTML5 Icon" class="w-12 h-12"><span class="font-medium">HTML5</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/css3/css3-original.svg" alt="CSS3 Icon" class="w-12 h-12"><span class="font-medium">CSS3</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg" alt="JavaScript Icon" class="w-12 h-12"><span class="font-medium">JavaScript</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg" alt="React Icon" class="w-12 h-12"><span class="font-medium">React.js</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg" alt="Next.js Icon" class="w-12 h-12 bg-white rounded-full p-1"><span class="font-medium">Next.js</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg" alt="Node.js Icon" class="w-12 h-12"><span class="font-medium">Node.js</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" alt="Python Icon" class="w-12 h-12"><span class="font-medium">Python</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg" alt="MySQL Icon" class="w-12 h-12"><span class="font-medium">MySQL</span></div>                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg" alt="Git Icon" class="w-12 h-12"><span class="font-medium">Git</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://bitcoin.org/img/icons/opengraph.png" alt="Bitcoin Icon" class="w-12 h-12"><span class="font-medium">Bitcoin</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg" alt="PHP Icon" class="w-12 h-12"><span class="font-medium">PHP</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/raspberrypi/raspberrypi-original.svg" alt="Raspberry Pi Icon" class="w-12 h-12"><span class="font-medium">Raspberry Pi</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://svgl.app/library/laravel.svg" alt="Laravel Icon" class="w-12 h-12"><span class="font-medium">Laravel</span></div>
                </div>

                <h2 class="text-4xl font-bold text-center text-white mb-16 mt-24"><span class="text-emerald-300">Tools</span> I Use Daily</h2>
                <div class="max-w-6xl mx-auto grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/windows8/windows8-original.svg" alt="Windows Icon" class="w-12 h-12"><span class="font-medium">Windows</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.kali.org/images/kali-dragon-icon.svg" alt="Kali Linux Icon" class="w-12 h-12"><span class="font-medium">Kali Linux</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/android/android-original.svg" alt="Android Icon" class="w-12 h-12"><span class="font-medium">Android</span></div>                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vscode/vscode-original.svg" alt="VSCode Icon" class="w-12 h-12"><span class="font-medium">VSCode</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.cursor.so/favicon.ico" alt="Cursor Icon" class="w-12 h-12 rounded-lg"><span class="font-medium">Cursor</span></div>                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg" alt="ChatGPT Icon" class="w-12 h-12"><span class="font-medium">ChatGPT</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://svgl.app/library/deepseek.svg" alt="DeepSeek Icon" class="w-12 h-12"><span class="font-medium">DeepSeek</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://claude.ai/images/claude_app_icon.png" alt="Claude Icon" class="w-12 h-12 rounded-lg"><span class="font-medium">Claude</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.perplexity.ai/favicon.svg" alt="Perplexity Icon" class="w-12 h-12"><span class="font-medium">Perplexity</span></div>                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png" alt="Copilot Icon" class="w-12 h-12 rounded-full bg-white p-1"><span class="font-medium">Copilot</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.gstatic.com/lamda/images/gemini_sparkle_v002_d4735304ff6292a690345.svg" alt="Gemini Icon" class="w-12 h-12"><span class="font-medium">Gemini</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://svgl.app/library/lovable.svg" alt="Lovable AI Icon" class="w-12 h-12"><span class="font-medium">Lovable AI</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://svgl.app/library/ubuntu.svg" alt="Ubuntu Icon" class="w-12 h-12"><span class="font-medium">Ubuntu</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://svgl.app/library/firebase.svg" alt="Firebase Icon" class="w-12 h-12"><span class="font-medium">Firebase</span></div>
                </div>
            </section>

            <!-- Section Proyek -->
            <section id="projects" class="page-content hidden py-24">
                <div class="max-w-7xl mx-auto">
                    <div class="text-center mb-16">
                        <h2 class="text-4xl font-bold text-white mb-4">My Recent <span class="text-emerald-300">Projects</span></h2>
                        <p class="text-xl text-gray-300 max-w-2xl mx-auto">Here are some projects I've been working on. Each one represents a learning milestone and showcases different aspects of my development journey.</p>
                    </div>

                    <!-- Featured Project -->
                    <div id="featured-project" class="liquid-glass-effect p-8 mb-16 tilt-card">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                            <div>
                                <div class="inline-block px-4 py-2 bg-emerald-600/20 text-emerald-300 rounded-full text-sm mb-4">Featured Project</div>
                                <h3 class="text-3xl font-bold text-white mb-4">TKJ Axioo Class Website</h3>
                                <p class="text-gray-300 text-lg mb-6">A comprehensive class website featuring dynamic content, dark/light theme switching, and smooth animations. Built entirely with vanilla HTML, CSS, and JavaScript to showcase fundamental web development skills.</p>
                                <div class="flex flex-wrap gap-2 mb-6">
                                    <span class="px-3 py-1 bg-orange-600/20 text-orange-300 rounded-full text-sm">HTML5</span>
                                    <span class="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">CSS3</span>
                                    <span class="px-3 py-1 bg-yellow-600/20 text-yellow-300 rounded-full text-sm">JavaScript</span>
                                    <span class="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">Responsive</span>
                                </div>
                                <div class="flex gap-4">
                                    <a href="https://github.com/rfypych/xtkjaxioo" target="_blank" class="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
                                        View Code
                                    </a>
                                    <a href="https://rfypych.github.io/xtkjaxioo/" target="_blank" class="border-2 border-emerald-600 text-emerald-400 hover:bg-emerald-600 hover:text-white font-bold py-3 px-6 rounded-full transition-all duration-300">
                                        Live Demo
                                    </a>
                                </div>
                            </div>
                            <div class="relative">
                                <img src="https://placehold.co/600x400/059669/d1fae5?text=TKJ-Axioo" alt="TKJ Axioo Website" class="w-full h-64 object-cover rounded-2xl shadow-lg">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Other Projects Grid -->
                    <div id="other-projects" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div class="liquid-glass-effect overflow-hidden shadow-lg flex flex-col tilt-card">
                            <img src="https://placehold.co/600x400/059669/d1fae5?text=Cypher-Chain" alt="Cypher-Chain Project" class="w-full h-48 object-cover">
                            <div class="p-6 flex flex-col flex-grow">
                                <h3 class="text-xl font-bold text-white mb-3">Cypher-Chain</h3>
                                <p class="text-gray-300 mb-4 flex-grow text-sm">A real-time blockchain visualization tool built with React and WebSockets. Features live transaction tracking and comprehensive block data analysis.</p>
                                <div class="flex flex-wrap gap-1 mb-4">
                                    <span class="px-2 py-1 bg-blue-600/20 text-blue-300 rounded text-xs">React</span>
                                    <span class="px-2 py-1 bg-green-600/20 text-green-300 rounded text-xs">WebSocket</span>
                                    <span class="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">Blockchain</span>
                                </div>
                                <a href="https://github.com/rfypych/cypher-chain" target="_blank" class="text-emerald-400 hover:text-emerald-300 font-semibold text-sm">View Project &rarr;</a>
                            </div>
                        </div>

                        <div class="liquid-glass-effect overflow-hidden shadow-lg flex flex-col tilt-card">
                            <img src="https://placehold.co/600x400/059669/d1fae5?text=Phish-Guard" alt="Phish-Guard Project" class="w-full h-48 object-cover">
                            <div class="p-6 flex flex-col flex-grow">
                                <h3 class="text-xl font-bold text-white mb-3">Phish-Guard</h3>
                                <p class="text-gray-300 mb-4 flex-grow text-sm">A browser extension utilizing machine learning algorithms to detect and prevent phishing attacks through intelligent URL pattern analysis.</p>
                                <div class="flex flex-wrap gap-1 mb-4">
                                    <span class="px-2 py-1 bg-yellow-600/20 text-yellow-300 rounded text-xs">JavaScript</span>
                                    <span class="px-2 py-1 bg-red-600/20 text-red-300 rounded text-xs">ML</span>
                                    <span class="px-2 py-1 bg-blue-600/20 text-blue-300 rounded text-xs">Extension</span>
                                </div>
                                <a href="https://github.com/rfypych/phish-guard" target="_blank" class="text-emerald-400 hover:text-emerald-300 font-semibold text-sm">View Project &rarr;</a>
                            </div>
                        </div>

                        <div class="liquid-glass-effect overflow-hidden shadow-lg flex flex-col tilt-card">
                            <img src="https://placehold.co/600x400/059669/d1fae5?text=Vuln-Scanner" alt="Vuln-Scanner Project" class="w-full h-48 object-cover">
                            <div class="p-6 flex flex-col flex-grow">
                                <h3 class="text-xl font-bold text-white mb-3">Vuln-Scanner</h3>
                                <p class="text-gray-300 mb-4 flex-grow text-sm">A comprehensive Python-based CLI tool for network security assessment, identifying vulnerabilities and potential security risks.</p>
                                <div class="flex flex-wrap gap-1 mb-4">
                                    <span class="px-2 py-1 bg-green-600/20 text-green-300 rounded text-xs">Python</span>
                                    <span class="px-2 py-1 bg-red-600/20 text-red-300 rounded text-xs">Security</span>
                                    <span class="px-2 py-1 bg-gray-600/20 text-gray-300 rounded text-xs">CLI</span>
                                </div>
                                <a href="https://github.com/rfypych/vuln-scanner" target="_blank" class="text-emerald-400 hover:text-emerald-300 font-semibold text-sm">View Project &rarr;</a>
                            </div>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="text-center mt-16">
                        <div class="liquid-glass-effect p-8 tilt-card">
                            <h3 class="text-2xl font-bold text-white mb-4">Interested in More?</h3>
                            <p class="text-gray-300 mb-6">Explore my GitHub repository for additional projects, contributions, and code samples!</p>
                            <a href="https://github.com/rfypych" target="_blank" class="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 inline-block">
                                Visit My GitHub
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Kontak -->
            <section id="contact" class="page-content hidden py-24 text-center">
                 <div class="max-w-2xl mx-auto liquid-glass-effect p-1 shadow-2xl tilt-card terminal-window overflow-hidden">
                    <div class="terminal-header p-2 rounded-t-[1.4rem] flex items-center gap-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div id="terminal-body" class="terminal-body p-4 text-left h-64 rounded-b-[1.4rem] overflow-y-auto">
                        <!-- Konten terminal akan diisi oleh JS -->
                    </div>
                 </div>
            </section>
        </main>
        
        <!-- Footer -->
        <footer class="text-center py-10 mt-12 border-t border-white/10 space-y-2">
            <p class="text-gray-300">Crafted with passion by Rofikul Huda 🚀</p>
            <p class="text-gray-400 text-sm">© 2025 rfypych. All rights reserved.</p>
        </footer>

    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.7.77/Tone.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- Variabel Global ---
            const preloader = document.getElementById('preloader');
            const navLinks = document.querySelectorAll('.nav-link');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
            const pages = document.querySelectorAll('.page-content');
            const introButton = document.getElementById('intro-button');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const tiltCards = document.querySelectorAll('.tilt-card');
            const typewriterContainer = document.getElementById('typewriter-container');

            // --- Terminal Loading Animation ---
            const terminalLoading = document.getElementById('terminal-loading');
            const progressFill = document.getElementById('progress-fill');

            const loadingSequence = [
                { type: 'prompt', text: 'initializing system...', delay: 500 },
                { type: 'output', text: 'Loading core modules...', delay: 800 },
                { type: 'success', text: '✓ Matrix engine loaded', delay: 600 },
                { type: 'success', text: '✓ Cyberpunk theme activated', delay: 600 },
                { type: 'success', text: '✓ Hacker mode enabled', delay: 600 },
                { type: 'prompt', text: 'scanning network...', delay: 700 },
                { type: 'output', text: 'Connecting to rfiklz.my.id...', delay: 800 },
                { type: 'success', text: '✓ Connection established', delay: 600 },
                { type: 'prompt', text: 'loading user profile...', delay: 700 },
                { type: 'success', text: '✓ Rofikul Huda profile loaded', delay: 600 },
                { type: 'success', text: '✓ Skills database synchronized', delay: 600 },
                { type: 'success', text: '✓ Projects repository accessed', delay: 600 },
                { type: 'prompt', text: 'finalizing setup...', delay: 800 },
                { type: 'success', text: '✓ All systems operational', delay: 600 },
                { type: 'success', text: 'Welcome to the Matrix! 🚀', delay: 1000 }
            ];

            let currentStep = 0;
            let totalSteps = loadingSequence.length;

            function typeTerminalText(element, text, callback) {
                let i = 0;
                element.innerHTML = '';

                function typeChar() {
                    if (i < text.length) {
                        element.innerHTML += text.charAt(i);
                        i++;
                        setTimeout(typeChar, 30 + Math.random() * 50);
                    } else {
                        element.innerHTML += '<span class="cursor-blink"></span>';
                        setTimeout(() => {
                            element.querySelector('.cursor-blink')?.remove();
                            if (callback) callback();
                        }, 300);
                    }
                }
                typeChar();
            }

            function runLoadingSequence() {
                if (currentStep >= loadingSequence.length) {
                    setTimeout(() => {
                        preloader.style.opacity = '0';
                        preloader.style.visibility = 'hidden';
                        setTimeout(() => {
                            typewriterEffect();
                        }, 500);
                    }, 1000);
                    return;
                }

                const step = loadingSequence[currentStep];
                const lineDiv = document.createElement('div');
                lineDiv.className = `loading-line ${step.type}`;
                terminalLoading.appendChild(lineDiv);

                // Update progress bar
                const progress = ((currentStep + 1) / totalSteps) * 100;
                progressFill.style.width = progress + '%';

                typeTerminalText(lineDiv, step.text, () => {
                    currentStep++;
                    setTimeout(runLoadingSequence, step.delay);
                });

                // Auto scroll to bottom
                terminalLoading.scrollTop = terminalLoading.scrollHeight;
            }

            // Start loading sequence after a short delay
            setTimeout(runLoadingSequence, 1000);

            // --- Real GitHub Contribution Graph Generator ---
            async function generateContributionGraph() {
                const contributionGraph = document.getElementById('contribution-graph');
                const totalElement = document.getElementById('total-contributions');

                if (!contributionGraph) return;

                try {
                    // Clear loading state
                    contributionGraph.innerHTML = '';

                    // Fetch real GitHub contribution data using GitHub's GraphQL API
                    const response = await fetch('https://github-contributions-api.jogruber.de/v4/rfypych');
                    const data = await response.json();

                    if (data && data.contributions) {
                        const contributions = data.contributions;
                        const grid = document.createElement('div');
                        grid.className = 'contribution-grid';

                        let totalContributions = 0;

                        contributions.forEach(week => {
                            week.days.forEach(day => {
                                const dayElement = document.createElement('div');
                                dayElement.className = 'contribution-day';

                                // Map contribution count to level (0-4)
                                let level = 0;
                                if (day.contributionCount > 0) {
                                    if (day.contributionCount >= 10) level = 4;
                                    else if (day.contributionCount >= 7) level = 3;
                                    else if (day.contributionCount >= 4) level = 2;
                                    else level = 1;
                                }

                                dayElement.classList.add(`contribution-level-${level}`);
                                dayElement.title = `${day.contributionCount} contributions on ${day.date}`;

                                totalContributions += day.contributionCount;
                                grid.appendChild(dayElement);
                            });
                        });

                        contributionGraph.appendChild(grid);

                        // Update total contributions with real data
                        if (totalElement) {
                            animateNumber(totalElement, 0, totalContributions, 2000);
                        }

                        console.log(`✅ Loaded ${totalContributions} real GitHub contributions`);
                    } else {
                        throw new Error('No contribution data available');
                    }
                } catch (error) {
                    console.log('Failed to fetch GitHub contributions, using fallback:', error);

                    // Fallback: Use GitHub's contribution graph image
                    contributionGraph.innerHTML = `
                        <div class="text-center">
                            <img src="https://ghchart.rshah.org/10B981/rfypych"
                                 alt="GitHub Contribution Graph"
                                 class="mx-auto rounded-lg shadow-lg max-w-full"
                                 style="filter: brightness(1.2) contrast(1.1);">
                        </div>
                    `;

                    // Fetch basic GitHub stats for total contributions estimate
                    try {
                        const userResponse = await fetch('https://api.github.com/users/rfypych');
                        const userData = await userResponse.json();

                        if (userData && totalElement) {
                            // Estimate based on account age and public repos
                            const accountAge = new Date().getFullYear() - new Date(userData.created_at).getFullYear();
                            const estimatedContributions = Math.floor(userData.public_repos * 20 + accountAge * 100);
                            animateNumber(totalElement, 0, estimatedContributions, 2000);
                        }
                    } catch (fallbackError) {
                        if (totalElement) {
                            totalElement.textContent = '500+';
                        }
                    }
                }
            }


            
            // --- Sound Engine ---
            let audioContextStarted = false;
            const synth = new Tone.Synth({ oscillator: { type: 'sine' }, envelope: { attack: 0.005, decay: 0.1, sustain: 0.3, release: 1 } }).toDestination();
            const typeSound = new Tone.MembraneSynth({ pitchDecay: 0.008, aattack: 0.001, decay: 0.1, sustain: 0.01, release: 0.1, octaves: 2}).toDestination();
            
            function startAudioContext() {
                if (audioContextStarted) return;
                Tone.start().then(() => {
                    audioContextStarted = true;
                });
            }

            function throttle(func, limit) {
                let inThrottle;
                return function() {
                    if (!inThrottle) {
                        func.apply(this, arguments);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }
            
            const throttledHoverSound = throttle(() => synth.triggerAttackRelease("C2", "8n"), 100);
            const throttledNavigateSound = throttle(() => synth.triggerAttackRelease("G2", "8n"), 100);
            const throttledTypeSound = throttle(() => typeSound.triggerAttack("C1", Tone.now(), 0.5), 80);

            // --- Efek Matrix ---
            const canvas = document.getElementById('matrix-canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            const katakana = 'アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブヅプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン';
            const latin = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const nums = '0123456789';
            const alphabet = katakana + latin + nums;
            const fontSize = 16;
            const columns = canvas.width / fontSize;
            const rainDrops = Array.from({ length: columns }).map(() => 1);

            function drawMatrix() {
                ctx.fillStyle = 'rgba(10, 10, 16, 0.05)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#10B981';
                ctx.font = fontSize + 'px monospace';
                for (let i = 0; i < rainDrops.length; i++) {
                    const text = alphabet.charAt(Math.floor(Math.random() * alphabet.length));
                    ctx.fillText(text, i * fontSize, rainDrops[i] * fontSize);
                    if (rainDrops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        rainDrops[i] = 0;
                    }
                    rainDrops[i]++;
                }
            }
            setInterval(drawMatrix, 30);
            
            // --- Terminal Typewriter Animation ---
            const typewriterTexts = [
                "root@matrix:~$ whoami",
                ">> Ethical Hacker",
                "root@matrix:~$ cat /proc/skills",
                ">> Web Developer",
                ">> Cybersecurity Enthusiast",
                "root@matrix:~$ sudo access --mode=creative",
                ">> Code Architect",
                ">> Digital Innovator",
                "root@matrix:~$ nmap -sS target.vulnerability",
                ">> System Penetrator",
                ">> Bug Hunter",
                "root@matrix:~$ ./exploit --payload=innovation",
                ">> Terminal Wizard",
                ">> Matrix Navigator",
                "root@matrix:~$ echo 'Welcome to the Matrix'",
                ">> Ready to hack the future..."
            ];
            let currentTextIndex = 0;
            let currentCharIndex = 0;
            let isDeleting = false;

            function typeText(element, text, i, fnCallback) {
                if (i < text.length) {
                    element.innerHTML = text.substring(0, i + 1) + '<span class="blinking-cursor"></span>';
                    throttledTypeSound();
                    setTimeout(() => typeText(element, text, i + 1, fnCallback), 100);
                } else if (typeof fnCallback === 'function') {
                    element.querySelector('.blinking-cursor')?.remove();
                    setTimeout(fnCallback, 500);
                }
            }

            function typewriterEffect() {
                const currentText = typewriterTexts[currentTextIndex];

                // Add terminal-style prefix for command lines
                const isCommand = currentText.startsWith('root@matrix');
                const displayText = currentText;

                if (!isDeleting && currentCharIndex <= currentText.length) {
                    const typedText = displayText.substring(0, currentCharIndex);

                    // Different styling for commands vs outputs
                    if (isCommand) {
                        typewriterContainer.innerHTML = `<span class="terminal-prompt">${typedText}</span><span class="blinking-cursor">█</span>`;
                    } else {
                        typewriterContainer.innerHTML = `<span class="terminal-output">${typedText}</span><span class="blinking-cursor">█</span>`;
                    }
                    currentCharIndex++;

                    // Add typing sound effect
                    if (currentCharIndex > 0) {
                        throttledTypeSound();
                    }

                    // Variable typing speed for more realistic effect
                    const baseSpeed = isCommand ? 60 : 100;
                    const randomDelay = baseSpeed + Math.random() * 40;
                    setTimeout(typewriterEffect, randomDelay);

                } else if (isDeleting && currentCharIndex >= 0) {
                    const typedText = displayText.substring(0, currentCharIndex);

                    // Different styling for commands vs outputs during deletion
                    if (isCommand) {
                        typewriterContainer.innerHTML = `<span class="terminal-prompt">${typedText}</span><span class="blinking-cursor">█</span>`;
                    } else {
                        typewriterContainer.innerHTML = `<span class="terminal-output">${typedText}</span><span class="blinking-cursor">█</span>`;
                    }
                    currentCharIndex--;

                    // Faster deletion speed
                    setTimeout(typewriterEffect, 30 + Math.random() * 20);

                } else if (!isDeleting && currentCharIndex > currentText.length) {
                    // Longer pause for commands, shorter for responses
                    const pauseTime = isCommand ? 3000 : 2500;
                    setTimeout(() => {
                        isDeleting = true;
                        typewriterEffect();
                    }, pauseTime);

                } else if (isDeleting && currentCharIndex < 0) {
                    isDeleting = false;
                    currentTextIndex = (currentTextIndex + 1) % typewriterTexts.length;

                    // Brief pause before next text
                    setTimeout(typewriterEffect, 800);
                }
            }
            
            // --- Animasi Terminal Kontak ---
            const terminalBody = document.getElementById('terminal-body');
            const contactCommands = [
                { type: 'command', text: 'whoami' },
                { type: 'output', text: 'Rofikul Huda - Developer & Tech Enthusiast' },
                { type: 'command', text: 'contact --list' },
                { type: 'output', text: 'Fetching contact information...' },
                { type: 'html', html: '<ul><li><a href="https://github.com/rfypych" target="_blank" class="text-emerald-300 hover:underline">GitHub: github.com/rfypych</a></li><li><a href="https://t.me/rfyycrnge" target="_blank" class="text-emerald-300 hover:underline">Telegram: t.me/rfyycrnge</a></li><li><a href="https://www.instagram.com/rfikl_" target="_blank" class="text-emerald-300 hover:underline">Instagram: instagram.com/rfikl_</a></li></ul>' },                { type: 'command', text: 'cat message.txt' },
                { type: 'output', text: "Let's connect and collaborate on exciting projects! 🚀" },
                { type: 'prompt' }
            ];

            // Icons configuration for the cursor
            document.documentElement.style.cursor = 'url("data:image/svg+xml,%3Csvg width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M5 12l5 5l9-9\' stroke=\'%2310B981\' stroke-width=\'2\'/%3E%3C/svg%3E"), auto';
              let isTerminalRunning = false;
            
            async function runTerminalSequence() {
                if (isTerminalRunning) return;
                isTerminalRunning = true;
                
                terminalBody.innerHTML = '';
                try {
                    for (const item of contactCommands) {
                        await handleTerminalItem(item);
                    }
                } finally {
                    isTerminalRunning = false;
                }
            }

            function handleTerminalItem(item) {
                return new Promise(resolve => {
                    if (!terminalBody) {
                        resolve();
                        return;
                    }

                    switch (item.type) {
                        case 'command':
                            const p = document.createElement('p');
                            p.innerHTML = `<span class="prompt"></span><span class="command-text"></span>`;
                            terminalBody.appendChild(p);
                            const commandSpan = p.querySelector('.command-text');
                            typeText(commandSpan, item.text, 0, resolve);
                            break;
                        case 'output':
                        case 'html':
                            const outputDiv = document.createElement('div');
                            outputDiv.className = item.type === 'output' ? 'py-1' : 'py-1';
                            outputDiv.innerHTML = item.text || item.html;
                            terminalBody.appendChild(outputDiv);
                            terminalBody.scrollTop = terminalBody.scrollHeight;
                            setTimeout(resolve, 300);
                            break;
                        case 'prompt':
                            const promptP = document.createElement('p');
                            promptP.innerHTML = `<span class="prompt"></span><span class="blinking-cursor"></span>`;
                            terminalBody.appendChild(promptP);
                            terminalBody.scrollTop = terminalBody.scrollHeight;
                            resolve();
                            break;
                    }
                });
            }

            // --- Navigasi Halaman ---
            function navigateTo(pageId, isInitialLoad = false) {
                if (!isInitialLoad) throttledNavigateSound();
                pages.forEach(page => page.classList.add('hidden'));
                const targetPage = document.getElementById(pageId);
                if (targetPage) targetPage.classList.remove('hidden');
                  if (pageId === 'hero') {
                    typewriterEffect();
                } else if (pageId === 'contact') {
                    // Clear and reset terminal before running sequence
                    if (terminalBody) {
                        terminalBody.innerHTML = '';
                        setTimeout(runTerminalSequence, 100);
                    }
                }

                navLinks.forEach(link => link.classList.toggle('nav-active', link.dataset.page === pageId));
                mobileMenu.classList.remove('open');
                mobileMenu.style.maxHeight = '0px';
            }
            
            // --- Event Listeners ---
            window.addEventListener('load', () => {
                preloader.style.opacity = '0';
                preloader.style.visibility = 'hidden';
            });
            
            // Event listener untuk desktop nav & tombol intro (dengan suara)
            const allNavElements = [...navLinks, introButton, ...document.querySelectorAll('[data-page]')];
            allNavElements.forEach(link => {
                link.addEventListener('mouseover', () => {
                    startAudioContext();
                    throttledHoverSound();
                });
                link.addEventListener('click', function(event) {
                    startAudioContext();
                    event.preventDefault();
                    navigateTo(this.dataset.page || 'about');
                });
            });
            
            // Event listener untuk mobile nav (tanpa suara hover)
            mobileNavLinks.forEach(link => {
                 link.addEventListener('click', function(event) {
                    startAudioContext();
                    event.preventDefault();
                    navigateTo(this.dataset.page || 'about');
                });
            });

            mobileMenuButton.addEventListener('click', function() {
                startAudioContext();
                mobileMenu.classList.toggle('open');
                mobileMenu.style.maxHeight = mobileMenu.classList.contains('open') ? mobileMenu.scrollHeight + 'px' : '0px';
            });
            
            // Efek 3D Tilt
            tiltCards.forEach(card => {
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    const { width, height } = rect;
                    const rotateX = (y - height / 2) / (height / 2) * -4;
                    const rotateY = (x - width / 2) / (width / 2) * 4;
                    card.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.02)`;
                });
                card.addEventListener('mouseleave', () => card.style.transform = 'rotateX(0) rotateY(0) scale(1)');
            });

            // --- Inisialisasi ---
            navigateTo('hero', true);

            // Generate contribution graph and fetch GitHub data after page load
            setTimeout(() => {
                generateContributionGraph();
                fetchGitHubData();
            }, 2000);

            // --- Real GitHub Data Fetcher ---
            async function fetchGitHubData() {
                const commitsElement = document.getElementById('github-commits');

                try {
                    // First try to get current year contributions from GitHub contributions API
                    const currentYear = new Date().getFullYear();
                    const contributionsResponse = await fetch(`https://github-contributions-api.jogruber.de/v4/rfypych?y=${currentYear}`);
                    const contributionsData = await contributionsResponse.json();

                    if (contributionsData && contributionsData.total && contributionsData.total.lastYear) {
                        // Use real current year contributions
                        const currentYearContributions = contributionsData.total.lastYear;

                        if (commitsElement) {
                            animateNumber(commitsElement, 0, currentYearContributions, 2000);
                        }
                        return;
                    }

                    // Fallback: Use GitHub API for user data
                    const userResponse = await fetch('https://api.github.com/users/rfypych');
                    const userData = await userResponse.json();

                    if (userData) {
                        // Get more accurate data from user profile
                        const accountAge = new Date().getFullYear() - new Date(userData.created_at).getFullYear();
                        const repoCount = userData.public_repos;

                        // More realistic estimation based on GitHub activity patterns
                        const estimatedYearlyContributions = Math.floor(
                            repoCount * 25 + // Assume 25 commits per repo on average
                            Math.min(accountAge * 50, 300) + // Base activity, capped
                            Math.random() * 100 // Some variance
                        );

                        if (commitsElement) {
                            animateNumber(commitsElement, 0, estimatedYearlyContributions, 2000);
                        }
                    }
                } catch (error) {
                    console.log('GitHub API error, using fallback:', error);

                    // Final fallback
                    if (commitsElement) {
                        commitsElement.textContent = '500+';
                    }
                }
            }

            function animateNumber(element, start, end, duration) {
                const startTime = performance.now();

                function updateNumber(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Easing function for smooth animation
                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                    const current = Math.floor(start + (end - start) * easeOutQuart);

                    element.textContent = current.toLocaleString() + '+';

                    if (progress < 1) {
                        requestAnimationFrame(updateNumber);
                    }
                }

                requestAnimationFrame(updateNumber);
            }
        });
    </script>
</body>
</html>
